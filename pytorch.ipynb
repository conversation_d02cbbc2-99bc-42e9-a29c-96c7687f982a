import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
print(torch.__version__)

# Scalar
scalar = torch.tensor(7)
scalar

scalar.ndim

scalar.item() # Get the value of a scalar tensor

# Vector
vector = torch.tensor([7, 7])
vector

vector.ndim

vector.shape

# MATRIX
MATRIX = torch.tensor([[7, 8], 
                        [9, 10]])
MATRIX

MATRIX.ndim

MATRIX.shape

MATRIX[1,0]

# TENSOR
TENSOR = torch.tensor([[[1, 2, 3],
                        [3, 6, 9],
                        [2, 4, 5]]])

TENSOR

TENSOR.ndim

TENSOR.shape

TENSOR[0]

# Create a random tensor of size (3, 4)
random_tensor = torch.rand(3, 4)
random_tensor

random_tensor.ndim

# Create a random tensor with similar shape to an image tensor
random_image_size_tensor = torch.rand(size=(3, 224, 224)) # height, width, colour channels (R, G, B)
random_image_size_tensor.shape, random_image_size_tensor.ndim

# Create a tensor of all zeros
zeros = torch.zeros(size=(3, 4))
zeros

# Create a tensor of all ones
ones = torch.ones(size=(3, 4))
ones

ones.dtype

random_tensor.dtype

#Use torch.range()
torch.range(0, 10)

one_to_ten = torch.arange(1, 11)
one_to_ten

# Creating tensors like
ten_zeros = torch.zeros_like(input=one_to_ten)
ten_zeros

# Float 32 tensor
float_32_tensor = torch.tensor([3.0, 6.0, 9.0], dtype=None, device=None, requires_grad=False)
float_32_tensor

float_32_tensor.dtype

float_16_tensor = float_32_tensor.type(torch.float16)
float_16_tensor

float_16_tensor * float_32_tensor

